<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
        <meta name="viewport" content="width=device-width, minimal-ui, viewport-fit=cover, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
        <link rel="icon" type="image/png" href="assets/favicon.png" />

        <title>OGL • 切线空间可视化学习</title>
        <link href="assets/main.css" rel="stylesheet" />
        <style>
            .controls {
                position: absolute;
                top: 10px;
                left: 10px;
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 15px;
                border-radius: 8px;
                font-family: monospace;
                font-size: 12px;
                z-index: 100;
            }
            .controls h3 {
                margin: 0 0 10px 0;
                color: #4caf50;
            }
            .controls label {
                display: block;
                margin: 8px 0;
                cursor: pointer;
            }
            .controls input[type='checkbox'] {
                margin-right: 8px;
            }
            .controls input[type='range'] {
                width: 150px;
                margin-left: 10px;
            }
            .info {
                position: absolute;
                bottom: 10px;
                left: 10px;
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 15px;
                border-radius: 8px;
                font-family: monospace;
                font-size: 11px;
                max-width: 400px;
                z-index: 100;
            }
            .legend {
                position: absolute;
                top: 10px;
                right: 10px;
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 15px;
                border-radius: 8px;
                font-family: monospace;
                font-size: 12px;
                z-index: 100;
            }
            .legend div {
                margin: 5px 0;
                display: flex;
                align-items: center;
            }
            .legend .color-box {
                width: 20px;
                height: 12px;
                margin-right: 8px;
                border: 1px solid #666;
            }
        </style>
    </head>
    <body>
        <!-- 控制面板 -->
        <div class="controls">
            <h3>🎛️ 可视化控制</h3>
            <label> <input type="checkbox" id="showNormals" checked /> 显示法线向量 (蓝色) </label>
            <label> <input type="checkbox" id="showTangents" checked /> 显示切线向量 (红色) </label>
            <label> <input type="checkbox" id="showBitangents" checked /> 显示副切线向量 (绿色) </label>
            <label> <input type="checkbox" id="showUVGrid" /> 显示UV网格 </label>
            <label> <input type="checkbox" id="useNormalMap" /> 使用法线贴图 </label>
            <label> 向量长度: <input type="range" id="vectorScale" min="0.1" max="2" step="0.1" value="0.5" /> </label>
            <label> 法线强度: <input type="range" id="normalScale" min="0" max="2" step="0.1" value="1" /> </label>
        </div>

        <!-- 图例 -->
        <div class="legend">
            <h3>📊 向量图例</h3>
            <div>
                <div class="color-box" style="background: #ff0000"></div>
                切线 (T) - 纹理U方向
            </div>
            <div>
                <div class="color-box" style="background: #00ff00"></div>
                副切线 (B) - 纹理V方向
            </div>
            <div>
                <div class="color-box" style="background: #0000ff"></div>
                法线 (N) - 表面垂直方向
            </div>
            <div>
                <div class="color-box" style="background: #ffff00"></div>
                计算法线 - 动态计算结果
            </div>
        </div>

        <!-- 说明信息 -->
        <div class="info">
            <h3>🎓 切线空间学习指南</h3>
            <p><strong>🔍 什么是切线空间 (TBN)：</strong></p>
            <p>• <span style="color: #ff6666">T (Tangent)</span>: 沿UV的U方向，纹理水平方向</p>
            <p>• <span style="color: #66ff66">B (Bitangent)</span>: 沿UV的V方向，纹理垂直方向</p>
            <p>• <span style="color: #6666ff">N (Normal)</span>: 垂直于表面的法线方向</p>
            <br />
            <p><strong>📐 动态计算原理：</strong></p>
            <p style="font-family: monospace; font-size: 10px; background: #333; padding: 5px; border-radius: 3px">
                vec3 pos_dx = dFdx(vMPos.xyz);<br />
                vec3 pos_dy = dFdy(vMPos.xyz);<br />
                vec2 tex_dx = dFdx(vUv);<br />
                vec2 tex_dy = dFdy(vUv);<br /><br />
                T = normalize(pos_dx * tex_dy.t - pos_dy * tex_dx.t);<br />
                B = normalize(-pos_dx * tex_dy.s + pos_dy * tex_dx.s);<br />
                N = normalize(cross(T, B));
            </p>
            <br />
            <p><strong>🎮 交互说明：</strong></p>
            <p>• 鼠标拖拽：旋转视角</p>
            <p>• 滚轮：缩放</p>
            <p>• 勾选选项：显示/隐藏不同向量</p>
            <p>• 启用法线贴图：查看动态计算效果</p>
        </div>

        <script type="module">
            import { Renderer, Camera, Transform, Orbit, Program, Mesh, Geometry, Vec3, Mat4, TextureLoader, Texture } from '../src/index.js';

            // ========== 初始化WebGL渲染器 ==========
            const renderer = new Renderer({ dpr: 2 });
            const gl = renderer.gl;
            document.body.appendChild(gl.canvas);

            // ========== 相机设置 ==========
            const camera = new Camera(gl, { fov: 45 });
            camera.position.set(3, 2, 5);

            // ========== 轨道控制器 ==========
            const controls = new Orbit(camera, {
                target: new Vec3(0, 0, 0),
            });

            // ========== 场景根节点 ==========
            const scene = new Transform();

            // ========== 响应式画布 ==========
            function resize() {
                renderer.setSize(window.innerWidth, window.innerHeight);
                camera.perspective({ aspect: gl.canvas.width / gl.canvas.height });
            }
            window.addEventListener('resize', resize, false);
            resize();

            // ========== 创建测试平面几何体 ==========
            function createTestPlane() {
                const size = 2;
                const segments = 8;
                const positions = [];
                const normals = [];
                const uvs = [];
                const indices = [];

                // 生成顶点数据
                for (let i = 0; i <= segments; i++) {
                    for (let j = 0; j <= segments; j++) {
                        const x = (i / segments - 0.5) * size;
                        const z = (j / segments - 0.5) * size;
                        const y = Math.sin(x * 2) * Math.cos(z * 2) * 0.2; // 添加一些波浪

                        positions.push(x, y, z);
                        normals.push(0, 1, 0); // 初始法线向上
                        uvs.push(i / segments, j / segments);
                    }
                }

                // 生成索引
                for (let i = 0; i < segments; i++) {
                    for (let j = 0; j < segments; j++) {
                        const a = i * (segments + 1) + j;
                        const b = a + 1;
                        const c = a + segments + 1;
                        const d = c + 1;

                        indices.push(a, b, c, b, d, c);
                    }
                }

                return new Geometry(gl, {
                    position: { size: 3, data: new Float32Array(positions) },
                    normal: { size: 3, data: new Float32Array(normals) },
                    uv: { size: 2, data: new Float32Array(uvs) },
                    index: { data: new Uint16Array(indices) },
                });
            }

            // ========== 主表面着色器 ==========
            const surfaceShader = {
                vertex: /* glsl */ `
                    attribute vec3 position;
                    attribute vec3 normal;
                    attribute vec2 uv;

                    uniform mat4 modelViewMatrix;
                    uniform mat4 projectionMatrix;
                    uniform mat3 normalMatrix;
                    uniform mat4 modelMatrix;

                    varying vec3 vNormal;
                    varying vec2 vUv;
                    varying vec3 vMPos;

                    void main() {
                        vNormal = normalize(normalMatrix * normal);
                        vUv = uv;
                        vMPos = (modelMatrix * vec4(position, 1.0)).xyz;
                        
                        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                    }
                `,
                fragment: /* glsl */ `
                    precision highp float;

                    uniform bool uUseNormalMap;
                    uniform float uNormalScale;
                    uniform sampler2D tNormal;

                    varying vec3 vNormal;
                    varying vec2 vUv;
                    varying vec3 vMPos;

                    // ========== 动态法线计算函数 ==========
                    vec3 getNormal() {
                        if (uUseNormalMap) {
                            // 使用动态切线计算
                            vec3 pos_dx = dFdx(vMPos.xyz);
                            vec3 pos_dy = dFdy(vMPos.xyz);
                            vec2 tex_dx = dFdx(vUv);
                            vec2 tex_dy = dFdy(vUv);
                            
                            vec3 t = normalize(pos_dx * tex_dy.t - pos_dy * tex_dx.t);
                            vec3 b = normalize(-pos_dx * tex_dy.s + pos_dy * tex_dx.s);
                            mat3 tbn = mat3(t, b, normalize(vNormal));
                            
                            vec3 n = texture2D(tNormal, vUv).rgb * 2.0 - 1.0;
                            n.xy *= uNormalScale;
                            return normalize(tbn * n);
                        } else {
                            return normalize(vNormal);
                        }
                    }

                    void main() {
                        vec3 normal = getNormal();
                        
                        // 简单的光照计算
                        vec3 lightDir = normalize(vec3(1.0, 1.0, 1.0));
                        float lighting = dot(normal, lightDir) * 0.5 + 0.5;
                        
                        // UV可视化
                        vec3 uvColor = vec3(vUv, 0.5);
                        
                        gl_FragColor = vec4(mix(uvColor, vec3(lighting), 0.7), 1.0);
                    }
                `,
            };

            // 创建几何体和网格
            const geometry = createTestPlane();
            const program = new Program(gl, surfaceShader);
            const mesh = new Mesh(gl, { geometry, program });
            mesh.setParent(scene);

            // ========== 创建法线贴图纹理 ==========
            function createNormalMapTexture() {
                const size = 256;
                const data = new Uint8Array(size * size * 4);

                for (let i = 0; i < size; i++) {
                    for (let j = 0; j < size; j++) {
                        const index = (i * size + j) * 4;

                        // 创建波浪法线贴图
                        const x = (i / size) * Math.PI * 4;
                        const y = (j / size) * Math.PI * 4;

                        const nx = Math.sin(x) * 0.5;
                        const ny = Math.sin(y) * 0.5;
                        const nz = Math.sqrt(1 - nx * nx - ny * ny);

                        data[index] = (nx * 0.5 + 0.5) * 255; // R
                        data[index + 1] = (ny * 0.5 + 0.5) * 255; // G
                        data[index + 2] = (nz * 0.5 + 0.5) * 255; // B
                        data[index + 3] = 255; // A
                    }
                }

                return new Texture(gl, {
                    image: {
                        data: data,
                        width: size,
                        height: size,
                    },
                    generateMipmaps: true,
                });
            }

            const normalTexture = createNormalMapTexture();

            // ========== 向量可视化系统 ==========
            class VectorVisualizer {
                constructor() {
                    this.vectorMeshes = [];
                    this.createVectorGeometry();
                }

                createVectorGeometry() {
                    // 创建箭头几何体（线段 + 箭头头部）
                    const linePositions = [
                        0,
                        0,
                        0, // 起点
                        0,
                        1,
                        0, // 终点
                    ];

                    // 箭头头部（小三角形）
                    const arrowPositions = [
                        0,
                        1,
                        0, // 箭头顶点
                        -0.05,
                        0.9,
                        0, // 左侧
                        0.05,
                        0.9,
                        0, // 右侧
                        0,
                        1,
                        0, // 箭头顶点
                        0,
                        0.9,
                        -0.05, // 后侧
                        0,
                        0.9,
                        0.05, // 前侧
                    ];

                    this.lineGeometry = new Geometry(gl, {
                        position: { size: 3, data: new Float32Array(linePositions) },
                    });

                    this.arrowGeometry = new Geometry(gl, {
                        position: { size: 3, data: new Float32Array(arrowPositions) },
                    });

                    const vectorShader = {
                        vertex: /* glsl */ `
                            attribute vec3 position;
                            uniform mat4 modelViewMatrix;
                            uniform mat4 projectionMatrix;
                            uniform vec3 uColor;
                            uniform float uThickness;
                            varying vec3 vColor;

                            void main() {
                                vColor = uColor;
                                gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                                gl_PointSize = uThickness;
                            }
                        `,
                        fragment: /* glsl */ `
                            precision highp float;
                            varying vec3 vColor;

                            void main() {
                                gl_FragColor = vec4(vColor, 0.8);
                            }
                        `,
                    };

                    this.vectorProgram = new Program(gl, vectorShader);
                }

                updateVectors(geometry, showNormals, showTangents, showBitangents, vectorScale) {
                    // 清除旧的向量
                    this.vectorMeshes.forEach((mesh) => {
                        if (mesh.parent) mesh.parent.removeChild(mesh);
                    });
                    this.vectorMeshes = [];

                    if (!showNormals && !showTangents && !showBitangents) return;

                    const positions = geometry.attributes.position.data;
                    const uvs = geometry.attributes.uv.data;
                    const step = 3; // 每隔几个顶点显示一个向量

                    for (let i = 0; i < positions.length; i += step * 3) {
                        const x = positions[i];
                        const y = positions[i + 1];
                        const z = positions[i + 2];

                        const u = uvs[(i / 3) * 2];
                        const v = uvs[(i / 3) * 2 + 1];

                        // 计算真实的切线空间向量
                        const pos = new Vec3(x, y, z);

                        // 模拟偏导数计算（基于UV坐标）
                        const deltaU = 0.01;
                        const deltaV = 0.01;

                        // 计算相邻位置的近似偏导数
                        const pos_du = new Vec3(1, 0, 0); // U方向的切线
                        const pos_dv = new Vec3(0, 0, 1); // V方向的切线

                        // 使用动态切线计算公式
                        const tangent = new Vec3();
                        tangent.copy(pos_du).normalize();

                        const bitangent = new Vec3();
                        bitangent.copy(pos_dv).normalize();

                        // 计算法线（叉积）
                        const normal = new Vec3();
                        normal.cross(tangent, bitangent).normalize();

                        if (showTangents) {
                            this.createVector(pos, tangent, new Vec3(1, 0, 0), vectorScale);
                        }
                        if (showBitangents) {
                            this.createVector(pos, bitangent, new Vec3(0, 1, 0), vectorScale);
                        }
                        if (showNormals) {
                            this.createVector(pos, normal, new Vec3(0, 0, 1), vectorScale);
                        }
                    }
                }

                createVector(position, direction, color, scale) {
                    // 创建线段
                    const lineMesh = new Mesh(gl, {
                        geometry: this.lineGeometry,
                        program: this.vectorProgram,
                        mode: gl.LINES,
                    });

                    // 创建箭头头部
                    const arrowMesh = new Mesh(gl, {
                        geometry: this.arrowGeometry,
                        program: this.vectorProgram,
                        mode: gl.LINES,
                    });

                    // 设置位置和方向
                    lineMesh.position.copy(position);
                    arrowMesh.position.copy(position);

                    // 计算旋转以对齐方向
                    const up = new Vec3(0, 1, 0);
                    const axis = new Vec3();
                    axis.cross(up, direction);
                    const angle = Math.acos(Math.max(-1, Math.min(1, up.dot(direction))));

                    if (axis.len() > 0.001) {
                        // 使用四元数的fromAxisAngle方法设置旋转
                        lineMesh.quaternion.fromAxisAngle(axis.normalize(), angle);
                        arrowMesh.quaternion.fromAxisAngle(axis.normalize(), angle);
                    }

                    lineMesh.scale.set(scale, scale, scale);
                    arrowMesh.scale.set(scale, scale, scale);

                    lineMesh.program.uniforms.uColor = { value: color };
                    lineMesh.program.uniforms.uThickness = { value: 3.0 };
                    arrowMesh.program.uniforms.uColor = { value: color };
                    arrowMesh.program.uniforms.uThickness = { value: 3.0 };

                    lineMesh.setParent(scene);
                    arrowMesh.setParent(scene);

                    this.vectorMeshes.push(lineMesh, arrowMesh);
                }
            }

            const vectorVisualizer = new VectorVisualizer();

            // ========== 控制面板事件 ==========
            const controls_ui = {
                showNormals: document.getElementById('showNormals'),
                showTangents: document.getElementById('showTangents'),
                showBitangents: document.getElementById('showBitangents'),
                showUVGrid: document.getElementById('showUVGrid'),
                useNormalMap: document.getElementById('useNormalMap'),
                vectorScale: document.getElementById('vectorScale'),
                normalScale: document.getElementById('normalScale'),
            };

            function updateVisualization() {
                vectorVisualizer.updateVectors(
                    geometry,
                    controls_ui.showNormals.checked,
                    controls_ui.showTangents.checked,
                    controls_ui.showBitangents.checked,
                    parseFloat(controls_ui.vectorScale.value)
                );

                program.uniforms.uUseNormalMap = { value: controls_ui.useNormalMap.checked };
                program.uniforms.uNormalScale = { value: parseFloat(controls_ui.normalScale.value) };
                program.uniforms.tNormal = { value: normalTexture };
            }

            // 绑定事件
            Object.values(controls_ui).forEach((control) => {
                control.addEventListener('change', updateVisualization);
                control.addEventListener('input', updateVisualization);
            });

            // 初始化
            updateVisualization();

            // ========== 渲染循环 ==========
            requestAnimationFrame(update);
            function update(t) {
                requestAnimationFrame(update);

                controls.update();
                renderer.render({ scene, camera });
            }
        </script>
    </body>
</html>
