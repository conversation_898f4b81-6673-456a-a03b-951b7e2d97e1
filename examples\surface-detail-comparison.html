<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>表面细节技术对比 - OGL示例</title>
        <style>
            body {
                margin: 0;
                padding: 0;
                background: #1a1a1a;
                font-family: 'Consolas', monospace;
                overflow: hidden;
            }

            canvas {
                display: block;
                cursor: grab;
            }

            canvas:active {
                cursor: grabbing;
            }

            .controls {
                position: absolute;
                top: 20px;
                left: 20px;
                background: rgba(0, 0, 0, 0.8);
                padding: 20px;
                border-radius: 8px;
                color: white;
                min-width: 300px;
            }

            .control-group {
                margin-bottom: 15px;
            }

            .control-group label {
                display: block;
                margin-bottom: 5px;
                font-size: 14px;
                color: #ccc;
            }

            .control-group select,
            .control-group input[type='range'] {
                width: 100%;
                padding: 5px;
                background: #333;
                border: 1px solid #555;
                border-radius: 4px;
                color: white;
            }

            .control-group input[type='range'] {
                height: 20px;
            }

            .info {
                position: absolute;
                bottom: 20px;
                left: 20px;
                background: rgba(0, 0, 0, 0.8);
                padding: 15px;
                border-radius: 8px;
                color: white;
                max-width: 400px;
                font-size: 12px;
                line-height: 1.4;
            }

            .technique-title {
                font-size: 16px;
                font-weight: bold;
                color: #4caf50;
                margin-bottom: 10px;
            }

            .value-display {
                display: inline-block;
                margin-left: 10px;
                color: #4caf50;
                font-weight: bold;
            }
        </style>
    </head>
    <body>
        <canvas id="canvas"></canvas>

        <div class="controls">
            <h3 style="margin-top: 0; color: #4caf50">表面细节技术对比</h3>

            <div class="control-group">
                <label for="technique">渲染技术:</label>
                <select id="technique">
                    <option value="bump">凹凸贴图 (Bump Mapping)</option>
                    <option value="normal" selected>法线贴图 (Normal Mapping)</option>
                    <option value="parallax">视差贴图 (Parallax Mapping)</option>
                    <option value="steep">陡峭视差贴图 (Steep Parallax)</option>
                </select>
            </div>

            <div class="control-group">
                <label for="intensity">强度: <span class="value-display" id="intensityValue">1.0</span></label>
                <input type="range" id="intensity" min="0" max="3" step="0.1" value="1.0" />
            </div>

            <div class="control-group">
                <label for="lightIntensity">光照强度: <span class="value-display" id="lightValue">1.0</span></label>
                <input type="range" id="lightIntensity" min="0" max="2" step="0.1" value="1.0" />
            </div>

            <div class="control-group">
                <label> <input type="checkbox" id="showWireframe" /> 显示线框 </label>
            </div>

            <div class="control-group">
                <label> <input type="checkbox" id="animateLight" checked /> 动画光源 </label>
            </div>
        </div>

        <div class="info">
            <div class="technique-title" id="techniqueTitle">法线贴图 (Normal Mapping)</div>
            <div id="techniqueDescription">通过存储在切线空间中的法线信息来扰动表面反射光，创造凹凸感。 需要光照才能看到效果，是游戏和实时渲染中的标准技术。</div>
        </div>

        <script type="module">
            import { Renderer, Camera, Transform, Program, Mesh, Geometry, Texture } from '../src/index.js';
            import { Orbit } from '../src/extras/Orbit.js';

            // ========== 初始化WebGL ==========
            const canvas = document.getElementById('canvas');
            const renderer = new Renderer({ canvas, width: window.innerWidth, height: window.innerHeight });
            const gl = renderer.gl;

            const camera = new Camera(gl, { fov: 45 });
            camera.position.set(0, 0, 5);

            const controls = new Orbit(camera, { element: canvas });

            const scene = new Transform();

            // ========== 创建纹理 ==========
            function createBrickTextures() {
                const size = 256;

                // 创建砖块漫反射纹理
                const diffuseData = new Uint8Array(size * size * 4);
                // 创建高度图
                const heightData = new Uint8Array(size * size * 4);
                // 创建法线贴图
                const normalData = new Uint8Array(size * size * 4);

                for (let y = 0; y < size; y++) {
                    for (let x = 0; x < size; x++) {
                        const i = (y * size + x) * 4;

                        // 砖块图案
                        const brickX = Math.floor(x / 32);
                        const brickY = Math.floor(y / 32);
                        const offsetX = (brickY % 2) * 16;
                        const localX = (x + offsetX) % 32;
                        const localY = y % 32;

                        const isBrick = localX > 2 && localX < 30 && localY > 2 && localY < 30;
                        const height = isBrick ? 0.8 : 0.2;

                        // 漫反射颜色
                        if (isBrick) {
                            diffuseData[i] = 180; // R
                            diffuseData[i + 1] = 120; // G
                            diffuseData[i + 2] = 80; // B
                        } else {
                            diffuseData[i] = 100; // R
                            diffuseData[i + 1] = 100; // G
                            diffuseData[i + 2] = 100; // B
                        }
                        diffuseData[i + 3] = 255; // A

                        // 高度图
                        const heightValue = height * 255;
                        heightData[i] = heightValue;
                        heightData[i + 1] = heightValue;
                        heightData[i + 2] = heightValue;
                        heightData[i + 3] = 255;

                        // 法线贴图 (简化计算)
                        const nx = isBrick ? 0.5 : localX < 16 ? 0.3 : 0.7;
                        const ny = isBrick ? 0.5 : localY < 16 ? 0.3 : 0.7;
                        const nz = 0.8;

                        normalData[i] = (nx * 0.5 + 0.5) * 255; // R
                        normalData[i + 1] = (ny * 0.5 + 0.5) * 255; // G
                        normalData[i + 2] = (nz * 0.5 + 0.5) * 255; // B
                        normalData[i + 3] = 255; // A
                    }
                }

                return {
                    diffuse: new Texture(gl, {
                        image: { data: diffuseData, width: size, height: size },
                        generateMipmaps: true,
                    }),
                    height: new Texture(gl, {
                        image: { data: heightData, width: size, height: size },
                        generateMipmaps: true,
                    }),
                    normal: new Texture(gl, {
                        image: { data: normalData, width: size, height: size },
                        generateMipmaps: true,
                    }),
                };
            }

            const textures = createBrickTextures();

            // ========== 创建几何体 ==========
            function createPlaneGeometry() {
                const size = 2;
                const segments = 64;
                const positions = [];
                const normals = [];
                const uvs = [];
                const tangents = [];
                const indices = [];

                for (let y = 0; y <= segments; y++) {
                    for (let x = 0; x <= segments; x++) {
                        const u = x / segments;
                        const v = y / segments;

                        positions.push((u - 0.5) * size, (v - 0.5) * size, 0);

                        normals.push(0, 0, 1);
                        tangents.push(1, 0, 0);
                        uvs.push(u * 4, v * 4); // 重复4次
                    }
                }

                for (let y = 0; y < segments; y++) {
                    for (let x = 0; x < segments; x++) {
                        const a = y * (segments + 1) + x;
                        const b = y * (segments + 1) + x + 1;
                        const c = (y + 1) * (segments + 1) + x;
                        const d = (y + 1) * (segments + 1) + x + 1;

                        indices.push(a, b, c, b, d, c);
                    }
                }

                return new Geometry(gl, {
                    position: { size: 3, data: new Float32Array(positions) },
                    normal: { size: 3, data: new Float32Array(normals) },
                    tangent: { size: 3, data: new Float32Array(tangents) },
                    uv: { size: 2, data: new Float32Array(uvs) },
                    index: { data: new Uint16Array(indices) },
                });
            }

            const geometry = createPlaneGeometry();

            // ========== 着色器程序 ==========
            const shaders = {
                bump: {
                    vertex: /* glsl */ `
                    attribute vec3 position;
                    attribute vec3 normal;
                    attribute vec2 uv;
                    
                    uniform mat4 modelViewMatrix;
                    uniform mat4 projectionMatrix;
                    uniform mat3 normalMatrix;
                    
                    varying vec3 vNormal;
                    varying vec2 vUv;
                    varying vec3 vPosition;
                    
                    void main() {
                        vNormal = normalize(normalMatrix * normal);
                        vUv = uv;
                        vPosition = (modelViewMatrix * vec4(position, 1.0)).xyz;
                        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                    }
                `,
                    fragment: /* glsl */ `
                    precision mediump float;
                    
                    uniform sampler2D uDiffuseMap;
                    uniform sampler2D uHeightMap;
                    uniform float uIntensity;
                    uniform float uLightIntensity;
                    uniform vec3 uLightPos;
                    
                    varying vec3 vNormal;
                    varying vec2 vUv;
                    varying vec3 vPosition;
                    
                    vec3 calculateBumpNormal() {
                        float height = texture2D(uHeightMap, vUv).r;
                        float heightU = texture2D(uHeightMap, vUv + vec2(0.01, 0.0)).r;
                        float heightV = texture2D(uHeightMap, vUv + vec2(0.0, 0.01)).r;
                        
                        vec2 gradient = vec2(heightU - height, heightV - height) * uIntensity;
                        
                        vec3 normal = normalize(vNormal);
                        vec3 tangent = normalize(cross(normal, vec3(0.0, 1.0, 0.0)));
                        vec3 bitangent = cross(normal, tangent);
                        
                        return normalize(normal + gradient.x * tangent + gradient.y * bitangent);
                    }
                    
                    void main() {
                        vec3 normal = calculateBumpNormal();
                        vec3 diffuseColor = texture2D(uDiffuseMap, vUv).rgb;
                        
                        vec3 lightDir = normalize(uLightPos - vPosition);
                        float lighting = max(dot(normal, lightDir), 0.1) * uLightIntensity;
                        
                        gl_FragColor = vec4(diffuseColor * lighting, 1.0);
                    }
                `,
                },

                normal: {
                    vertex: /* glsl */ `
                    attribute vec3 position;
                    attribute vec3 normal;
                    attribute vec3 tangent;
                    attribute vec2 uv;
                    
                    uniform mat4 modelViewMatrix;
                    uniform mat4 projectionMatrix;
                    uniform mat3 normalMatrix;
                    
                    varying vec3 vNormal;
                    varying vec3 vTangent;
                    varying vec3 vBitangent;
                    varying vec2 vUv;
                    varying vec3 vPosition;
                    
                    void main() {
                        vNormal = normalize(normalMatrix * normal);
                        vTangent = normalize(normalMatrix * tangent);
                        vBitangent = cross(vNormal, vTangent);
                        vUv = uv;
                        vPosition = (modelViewMatrix * vec4(position, 1.0)).xyz;
                        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                    }
                `,
                    fragment: /* glsl */ `
                    precision mediump float;
                    
                    uniform sampler2D uDiffuseMap;
                    uniform sampler2D uNormalMap;
                    uniform float uIntensity;
                    uniform float uLightIntensity;
                    uniform vec3 uLightPos;
                    
                    varying vec3 vNormal;
                    varying vec3 vTangent;
                    varying vec3 vBitangent;
                    varying vec2 vUv;
                    varying vec3 vPosition;
                    
                    vec3 getNormalFromMap() {
                        vec3 tangentNormal = texture2D(uNormalMap, vUv).xyz * 2.0 - 1.0;
                        tangentNormal.xy *= uIntensity;
                        
                        mat3 TBN = mat3(
                            normalize(vTangent),
                            normalize(vBitangent),
                            normalize(vNormal)
                        );
                        
                        return normalize(TBN * tangentNormal);
                    }
                    
                    void main() {
                        vec3 normal = getNormalFromMap();
                        vec3 diffuseColor = texture2D(uDiffuseMap, vUv).rgb;
                        
                        vec3 lightDir = normalize(uLightPos - vPosition);
                        vec3 viewDir = normalize(-vPosition);
                        vec3 reflectDir = reflect(-lightDir, normal);
                        
                        vec3 ambient = 0.1 * diffuseColor;
                        float diff = max(dot(normal, lightDir), 0.0);
                        vec3 diffuse = diff * diffuseColor * uLightIntensity;
                        float spec = pow(max(dot(viewDir, reflectDir), 0.0), 32.0);
                        vec3 specular = spec * vec3(0.3) * uLightIntensity;
                        
                        gl_FragColor = vec4(ambient + diffuse + specular, 1.0);
                    }
                `,
                },

                parallax: {
                    vertex: /* glsl */ `
                    attribute vec3 position;
                    attribute vec3 normal;
                    attribute vec3 tangent;
                    attribute vec2 uv;

                    uniform mat4 modelViewMatrix;
                    uniform mat4 projectionMatrix;
                    uniform mat3 normalMatrix;
                    uniform mat4 modelMatrix;
                    uniform vec3 uViewPos;

                    varying vec3 vNormal;
                    varying vec3 vTangent;
                    varying vec3 vBitangent;
                    varying vec2 vUv;
                    varying vec3 vPosition;
                    varying vec3 vTangentViewPos;
                    varying vec3 vTangentFragPos;

                    void main() {
                        vNormal = normalize(normalMatrix * normal);
                        vTangent = normalize(normalMatrix * tangent);
                        vBitangent = cross(vNormal, vTangent);
                        vUv = uv;

                        vec3 worldPos = (modelMatrix * vec4(position, 1.0)).xyz;
                        vPosition = (modelViewMatrix * vec4(position, 1.0)).xyz;

                        // 计算切线空间的观察位置和片元位置
                        mat3 TBN = transpose(mat3(vTangent, vBitangent, vNormal));
                        vTangentViewPos = TBN * uViewPos;
                        vTangentFragPos = TBN * worldPos;

                        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                    }
                `,
                    fragment: /* glsl */ `
                    precision mediump float;

                    uniform sampler2D uDiffuseMap;
                    uniform sampler2D uNormalMap;
                    uniform sampler2D uHeightMap;
                    uniform float uIntensity;
                    uniform float uLightIntensity;
                    uniform vec3 uLightPos;

                    varying vec3 vNormal;
                    varying vec3 vTangent;
                    varying vec3 vBitangent;
                    varying vec2 vUv;
                    varying vec3 vPosition;
                    varying vec3 vTangentViewPos;
                    varying vec3 vTangentFragPos;

                    vec2 parallaxMapping(vec2 texCoords, vec3 viewDir) {
                        float height = texture2D(uHeightMap, texCoords).r;
                        vec2 p = viewDir.xy / viewDir.z * (height * uIntensity * 0.1);
                        return texCoords - p;
                    }

                    void main() {
                        vec3 viewDir = normalize(vTangentViewPos - vTangentFragPos);
                        vec2 parallaxUV = parallaxMapping(vUv, viewDir);

                        // 边界检查
                        if (parallaxUV.x > 4.0 || parallaxUV.y > 4.0 || parallaxUV.x < 0.0 || parallaxUV.y < 0.0) {
                            parallaxUV = vUv;
                        }

                        vec3 diffuseColor = texture2D(uDiffuseMap, parallaxUV).rgb;
                        vec3 tangentNormal = texture2D(uNormalMap, parallaxUV).xyz * 2.0 - 1.0;

                        mat3 TBN = mat3(
                            normalize(vTangent),
                            normalize(vBitangent),
                            normalize(vNormal)
                        );

                        vec3 normal = normalize(TBN * tangentNormal);
                        vec3 lightDir = normalize(uLightPos - vPosition);
                        vec3 viewDirWorld = normalize(-vPosition);
                        vec3 reflectDir = reflect(-lightDir, normal);

                        vec3 ambient = 0.1 * diffuseColor;
                        float diff = max(dot(normal, lightDir), 0.0);
                        vec3 diffuse = diff * diffuseColor * uLightIntensity;
                        float spec = pow(max(dot(viewDirWorld, reflectDir), 0.0), 32.0);
                        vec3 specular = spec * vec3(0.3) * uLightIntensity;

                        gl_FragColor = vec4(ambient + diffuse + specular, 1.0);
                    }
                `,
                },

                steep: {
                    vertex: /* glsl */ `
                    attribute vec3 position;
                    attribute vec3 normal;
                    attribute vec3 tangent;
                    attribute vec2 uv;

                    uniform mat4 modelViewMatrix;
                    uniform mat4 projectionMatrix;
                    uniform mat3 normalMatrix;
                    uniform mat4 modelMatrix;
                    uniform vec3 uViewPos;

                    varying vec3 vNormal;
                    varying vec3 vTangent;
                    varying vec3 vBitangent;
                    varying vec2 vUv;
                    varying vec3 vPosition;
                    varying vec3 vTangentViewPos;
                    varying vec3 vTangentFragPos;

                    void main() {
                        vNormal = normalize(normalMatrix * normal);
                        vTangent = normalize(normalMatrix * tangent);
                        vBitangent = cross(vNormal, vTangent);
                        vUv = uv;

                        vec3 worldPos = (modelMatrix * vec4(position, 1.0)).xyz;
                        vPosition = (modelViewMatrix * vec4(position, 1.0)).xyz;

                        mat3 TBN = transpose(mat3(vTangent, vBitangent, vNormal));
                        vTangentViewPos = TBN * uViewPos;
                        vTangentFragPos = TBN * worldPos;

                        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                    }
                `,
                    fragment: /* glsl */ `
                    precision mediump float;

                    uniform sampler2D uDiffuseMap;
                    uniform sampler2D uNormalMap;
                    uniform sampler2D uHeightMap;
                    uniform float uIntensity;
                    uniform float uLightIntensity;
                    uniform vec3 uLightPos;

                    varying vec3 vNormal;
                    varying vec3 vTangent;
                    varying vec3 vBitangent;
                    varying vec2 vUv;
                    varying vec3 vPosition;
                    varying vec3 vTangentViewPos;
                    varying vec3 vTangentFragPos;

                    vec2 steepParallaxMapping(vec2 texCoords, vec3 viewDir) {
                        const float numLayers = 16.0;
                        float layerDepth = 1.0 / numLayers;
                        float currentLayerDepth = 0.0;

                        vec2 P = viewDir.xy * uIntensity * 0.1;
                        vec2 deltaTexCoords = P / numLayers;

                        vec2 currentTexCoords = texCoords;
                        float currentDepthMapValue = texture2D(uHeightMap, currentTexCoords).r;

                        while (currentLayerDepth < currentDepthMapValue) {
                            currentTexCoords -= deltaTexCoords;
                            currentDepthMapValue = texture2D(uHeightMap, currentTexCoords).r;
                            currentLayerDepth += layerDepth;
                        }

                        // 获取交点前的纹理坐标
                        vec2 prevTexCoords = currentTexCoords + deltaTexCoords;

                        // 线性插值
                        float afterDepth = currentDepthMapValue - currentLayerDepth;
                        float beforeDepth = texture2D(uHeightMap, prevTexCoords).r - currentLayerDepth + layerDepth;

                        float weight = afterDepth / (afterDepth - beforeDepth);
                        return prevTexCoords * weight + currentTexCoords * (1.0 - weight);
                    }

                    void main() {
                        vec3 viewDir = normalize(vTangentViewPos - vTangentFragPos);
                        vec2 parallaxUV = steepParallaxMapping(vUv, viewDir);

                        if (parallaxUV.x > 4.0 || parallaxUV.y > 4.0 || parallaxUV.x < 0.0 || parallaxUV.y < 0.0) {
                            parallaxUV = vUv;
                        }

                        vec3 diffuseColor = texture2D(uDiffuseMap, parallaxUV).rgb;
                        vec3 tangentNormal = texture2D(uNormalMap, parallaxUV).xyz * 2.0 - 1.0;

                        mat3 TBN = mat3(
                            normalize(vTangent),
                            normalize(vBitangent),
                            normalize(vNormal)
                        );

                        vec3 normal = normalize(TBN * tangentNormal);
                        vec3 lightDir = normalize(uLightPos - vPosition);
                        vec3 viewDirWorld = normalize(-vPosition);
                        vec3 reflectDir = reflect(-lightDir, normal);

                        vec3 ambient = 0.1 * diffuseColor;
                        float diff = max(dot(normal, lightDir), 0.0);
                        vec3 diffuse = diff * diffuseColor * uLightIntensity;
                        float spec = pow(max(dot(viewDirWorld, reflectDir), 0.0), 32.0);
                        vec3 specular = spec * vec3(0.3) * uLightIntensity;

                        gl_FragColor = vec4(ambient + diffuse + specular, 1.0);
                    }
                `,
                },
            };

            // 当前程序
            let currentProgram = new Program(gl, {
                vertex: shaders.normal.vertex,
                fragment: shaders.normal.fragment,
                uniforms: {
                    uDiffuseMap: { value: textures.diffuse },
                    uNormalMap: { value: textures.normal },
                    uHeightMap: { value: textures.height },
                    uIntensity: { value: 1.0 },
                    uLightIntensity: { value: 1.0 },
                    uLightPos: { value: [2, 2, 2] },
                    uViewPos: { value: [0, 0, 5] },
                },
            });

            const mesh = new Mesh(gl, { geometry, program: currentProgram });
            mesh.setParent(scene);

            // ========== 控制面板 ==========
            const controls_ui = {
                technique: document.getElementById('technique'),
                intensity: document.getElementById('intensity'),
                lightIntensity: document.getElementById('lightIntensity'),
                showWireframe: document.getElementById('showWireframe'),
                animateLight: document.getElementById('animateLight'),
                intensityValue: document.getElementById('intensityValue'),
                lightValue: document.getElementById('lightValue'),
                techniqueTitle: document.getElementById('techniqueTitle'),
                techniqueDescription: document.getElementById('techniqueDescription'),
            };

            const techniqueInfo = {
                bump: {
                    title: '凹凸贴图 (Bump Mapping)',
                    description: '使用高度图计算法线扰动，创造凹凸感。不需要光照就能工作，但有光照效果更明显。性能好但效果相对简单。',
                },
                normal: {
                    title: '法线贴图 (Normal Mapping)',
                    description: '直接存储法线信息来扰动反射光，创造凹凸感。必须有光照才能看到效果，是游戏和实时渲染中的标准技术。',
                },
                parallax: {
                    title: '视差贴图 (Parallax Mapping)',
                    description: '通过模拟深度视差效果，在不改变几何体的情况下创造逼真的深度感。效果比法线贴图更好但计算更复杂。',
                },
                steep: {
                    title: '陡峭视差贴图 (Steep Parallax)',
                    description: '使用分层搜索算法的高级视差贴图，能产生更准确的深度效果和正确的遮蔽关系。',
                },
            };

            function updateTechnique() {
                const technique = controls_ui.technique.value;
                const info = techniqueInfo[technique];

                controls_ui.techniqueTitle.textContent = info.title;
                controls_ui.techniqueDescription.textContent = info.description;

                // 更新着色器程序
                if (shaders[technique]) {
                    currentProgram = new Program(gl, {
                        vertex: shaders[technique].vertex,
                        fragment: shaders[technique].fragment,
                        uniforms: {
                            uDiffuseMap: { value: textures.diffuse },
                            uNormalMap: { value: textures.normal },
                            uHeightMap: { value: textures.height },
                            uIntensity: { value: parseFloat(controls_ui.intensity.value) },
                            uLightIntensity: { value: parseFloat(controls_ui.lightIntensity.value) },
                            uLightPos: { value: [2, 2, 2] },
                            uViewPos: { value: [0, 0, 5] },
                        },
                    });

                    mesh.program = currentProgram;
                }
            }

            function updateValues() {
                controls_ui.intensityValue.textContent = controls_ui.intensity.value;
                controls_ui.lightValue.textContent = controls_ui.lightIntensity.value;

                if (currentProgram.uniforms.uIntensity) {
                    currentProgram.uniforms.uIntensity.value = parseFloat(controls_ui.intensity.value);
                }
                if (currentProgram.uniforms.uLightIntensity) {
                    currentProgram.uniforms.uLightIntensity.value = parseFloat(controls_ui.lightIntensity.value);
                }
            }

            // 绑定事件
            controls_ui.technique.addEventListener('change', updateTechnique);
            controls_ui.intensity.addEventListener('input', updateValues);
            controls_ui.lightIntensity.addEventListener('input', updateValues);

            controls_ui.showWireframe.addEventListener('change', () => {
                gl.polygonMode && gl.polygonMode(gl.FRONT_AND_BACK, controls_ui.showWireframe.checked ? gl.LINE : gl.FILL);
            });

            // ========== 渲染循环 ==========
            let time = 0;

            function resize() {
                renderer.setSize(window.innerWidth, window.innerHeight);
                camera.perspective({ aspect: window.innerWidth / window.innerHeight });
            }

            window.addEventListener('resize', resize);
            resize();

            requestAnimationFrame(update);
            function update(t) {
                requestAnimationFrame(update);
                time = t * 0.001;

                // 动画光源
                if (controls_ui.animateLight.checked) {
                    const lightPos = [Math.cos(time) * 3, Math.sin(time * 0.7) * 2 + 1, Math.sin(time) * 3 + 2];

                    if (currentProgram.uniforms.uLightPos) {
                        currentProgram.uniforms.uLightPos.value = lightPos;
                    }
                }

                // 更新相机位置
                if (currentProgram.uniforms.uViewPos) {
                    currentProgram.uniforms.uViewPos.value = camera.position;
                }

                controls.update();
                renderer.render({ scene, camera });
            }
        </script>
    </body>
</html>
