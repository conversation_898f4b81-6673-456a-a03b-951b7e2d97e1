<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表面细节技术对比 - OGL示例</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #1a1a1a;
            font-family: 'Consolas', monospace;
            overflow: hidden;
        }
        
        canvas {
            display: block;
            cursor: grab;
        }
        
        canvas:active {
            cursor: grabbing;
        }
        
        .controls {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            border-radius: 8px;
            color: white;
            min-width: 300px;
        }
        
        .control-group {
            margin-bottom: 15px;
        }
        
        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 14px;
            color: #ccc;
        }
        
        .control-group select,
        .control-group input[type="range"] {
            width: 100%;
            padding: 5px;
            background: #333;
            border: 1px solid #555;
            border-radius: 4px;
            color: white;
        }
        
        .info {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 8px;
            color: white;
            max-width: 400px;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .technique-title {
            font-size: 16px;
            font-weight: bold;
            color: #4CAF50;
            margin-bottom: 10px;
        }
        
        .value-display {
            display: inline-block;
            margin-left: 10px;
            color: #4CAF50;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <canvas id="canvas"></canvas>
    
    <div class="controls">
        <h3 style="margin-top: 0; color: #4CAF50;">表面细节技术对比</h3>
        
        <div class="control-group">
            <label for="technique">渲染技术:</label>
            <select id="technique">
                <option value="basic" selected>基础着色</option>
                <option value="bump">凹凸贴图 (Bump Mapping)</option>
                <option value="normal">法线贴图 (Normal Mapping)</option>
            </select>
        </div>
        
        <div class="control-group">
            <label for="intensity">强度: <span class="value-display" id="intensityValue">1.0</span></label>
            <input type="range" id="intensity" min="0" max="3" step="0.1" value="1.0">
        </div>
        
        <div class="control-group">
            <label for="lightIntensity">光照强度: <span class="value-display" id="lightValue">1.0</span></label>
            <input type="range" id="lightIntensity" min="0" max="2" step="0.1" value="1.0">
        </div>
        
        <div class="control-group">
            <label>
                <input type="checkbox" id="animateLight" checked> 动画光源
            </label>
        </div>
    </div>
    
    <div class="info">
        <div class="technique-title" id="techniqueTitle">基础着色</div>
        <div id="techniqueDescription">
            标准的Phong光照模型，没有表面细节增强。
        </div>
    </div>

    <script type="module">
        import { Renderer, Camera, Transform, Program, Mesh, Geometry, Texture } from '../src/index.js';
        import { Orbit } from '../src/extras/Orbit.js';

        // ========== 初始化WebGL ==========
        const canvas = document.getElementById('canvas');
        const renderer = new Renderer({ canvas, width: window.innerWidth, height: window.innerHeight });
        const gl = renderer.gl;
        
        const camera = new Camera(gl, { fov: 45 });
        camera.position.set(0, 0, 5);
        
        const controls = new Orbit(camera, { element: canvas });
        const scene = new Transform();

        // ========== 创建简单纹理 ==========
        const whiteTexture = new Texture(gl);
        const normalTexture = new Texture(gl);

        // ========== 创建几何体 ==========
        function createPlaneGeometry() {
            const size = 2;
            const segments = 32;
            const positions = [];
            const normals = [];
            const uvs = [];
            const indices = [];
            
            for (let y = 0; y <= segments; y++) {
                for (let x = 0; x <= segments; x++) {
                    const u = x / segments;
                    const v = y / segments;
                    
                    positions.push(
                        (u - 0.5) * size,
                        (v - 0.5) * size,
                        0
                    );
                    
                    normals.push(0, 0, 1);
                    uvs.push(u * 4, v * 4);
                }
            }
            
            for (let y = 0; y < segments; y++) {
                for (let x = 0; x < segments; x++) {
                    const a = y * (segments + 1) + x;
                    const b = y * (segments + 1) + x + 1;
                    const c = (y + 1) * (segments + 1) + x;
                    const d = (y + 1) * (segments + 1) + x + 1;
                    
                    indices.push(a, b, c, b, d, c);
                }
            }
            
            return new Geometry(gl, {
                position: { size: 3, data: new Float32Array(positions) },
                normal: { size: 3, data: new Float32Array(normals) },
                uv: { size: 2, data: new Float32Array(uvs) },
                index: { data: new Uint16Array(indices) }
            });
        }
        
        const geometry = createPlaneGeometry();

        // ========== 着色器程序 ==========
        const shaders = {
            basic: {
                vertex: /* glsl */ `
                    attribute vec3 position;
                    attribute vec3 normal;
                    attribute vec2 uv;
                    
                    uniform mat4 modelViewMatrix;
                    uniform mat4 projectionMatrix;
                    uniform mat3 normalMatrix;
                    
                    varying vec3 vNormal;
                    varying vec2 vUv;
                    varying vec3 vPosition;
                    
                    void main() {
                        vNormal = normalize(normalMatrix * normal);
                        vUv = uv;
                        vPosition = (modelViewMatrix * vec4(position, 1.0)).xyz;
                        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                    }
                `,
                fragment: /* glsl */ `
                    precision mediump float;
                    
                    uniform float uLightIntensity;
                    uniform vec3 uLightPos;
                    
                    varying vec3 vNormal;
                    varying vec2 vUv;
                    varying vec3 vPosition;
                    
                    void main() {
                        vec3 normal = normalize(vNormal);
                        vec3 lightDir = normalize(uLightPos - vPosition);
                        float lighting = max(dot(normal, lightDir), 0.1) * uLightIntensity;
                        
                        // 简单的棋盘格图案
                        vec2 grid = floor(vUv);
                        float checker = mod(grid.x + grid.y, 2.0);
                        vec3 color = mix(vec3(0.3), vec3(0.7), checker);
                        
                        gl_FragColor = vec4(color * lighting, 1.0);
                    }
                `
            },
            
            bump: {
                vertex: /* glsl */ `
                    attribute vec3 position;
                    attribute vec3 normal;
                    attribute vec2 uv;
                    
                    uniform mat4 modelViewMatrix;
                    uniform mat4 projectionMatrix;
                    uniform mat3 normalMatrix;
                    
                    varying vec3 vNormal;
                    varying vec2 vUv;
                    varying vec3 vPosition;
                    
                    void main() {
                        vNormal = normalize(normalMatrix * normal);
                        vUv = uv;
                        vPosition = (modelViewMatrix * vec4(position, 1.0)).xyz;
                        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                    }
                `,
                fragment: /* glsl */ `
                    precision mediump float;
                    
                    uniform float uIntensity;
                    uniform float uLightIntensity;
                    uniform vec3 uLightPos;
                    
                    varying vec3 vNormal;
                    varying vec2 vUv;
                    varying vec3 vPosition;
                    
                    float getHeight(vec2 uv) {
                        vec2 grid = floor(uv);
                        float checker = mod(grid.x + grid.y, 2.0);
                        return checker * 0.5 + 0.25;
                    }
                    
                    vec3 calculateBumpNormal() {
                        float height = getHeight(vUv);
                        float heightU = getHeight(vUv + vec2(0.01, 0.0));
                        float heightV = getHeight(vUv + vec2(0.0, 0.01));
                        
                        vec2 gradient = vec2(heightU - height, heightV - height) * uIntensity;
                        
                        vec3 normal = normalize(vNormal);
                        vec3 tangent = normalize(cross(normal, vec3(0.0, 1.0, 0.0)));
                        vec3 bitangent = cross(normal, tangent);
                        
                        return normalize(normal + gradient.x * tangent + gradient.y * bitangent);
                    }
                    
                    void main() {
                        vec3 normal = calculateBumpNormal();
                        vec3 lightDir = normalize(uLightPos - vPosition);
                        float lighting = max(dot(normal, lightDir), 0.1) * uLightIntensity;
                        
                        vec2 grid = floor(vUv);
                        float checker = mod(grid.x + grid.y, 2.0);
                        vec3 color = mix(vec3(0.3), vec3(0.7), checker);
                        
                        gl_FragColor = vec4(color * lighting, 1.0);
                    }
                `
            },
            
            normal: {
                vertex: /* glsl */ `
                    attribute vec3 position;
                    attribute vec3 normal;
                    attribute vec2 uv;
                    
                    uniform mat4 modelViewMatrix;
                    uniform mat4 projectionMatrix;
                    uniform mat3 normalMatrix;
                    
                    varying vec3 vNormal;
                    varying vec2 vUv;
                    varying vec3 vPosition;
                    
                    void main() {
                        vNormal = normalize(normalMatrix * normal);
                        vUv = uv;
                        vPosition = (modelViewMatrix * vec4(position, 1.0)).xyz;
                        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                    }
                `,
                fragment: /* glsl */ `
                    precision mediump float;
                    
                    uniform float uIntensity;
                    uniform float uLightIntensity;
                    uniform vec3 uLightPos;
                    
                    varying vec3 vNormal;
                    varying vec2 vUv;
                    varying vec3 vPosition;
                    
                    vec3 getNormalFromPattern() {
                        // 程序化生成法线贴图效果
                        vec2 grid = fract(vUv) - 0.5;
                        float dist = length(grid);
                        
                        vec3 tangentNormal = vec3(
                            grid.x * uIntensity,
                            grid.y * uIntensity,
                            1.0 - dist * uIntensity * 0.5
                        );
                        
                        return normalize(tangentNormal);
                    }
                    
                    void main() {
                        vec3 tangentNormal = getNormalFromPattern();
                        
                        // 简化的切线空间变换
                        vec3 normal = normalize(vNormal);
                        vec3 tangent = normalize(cross(normal, vec3(0.0, 1.0, 0.0)));
                        vec3 bitangent = cross(normal, tangent);
                        mat3 TBN = mat3(tangent, bitangent, normal);
                        
                        vec3 worldNormal = normalize(TBN * tangentNormal);
                        
                        vec3 lightDir = normalize(uLightPos - vPosition);
                        vec3 viewDir = normalize(-vPosition);
                        vec3 reflectDir = reflect(-lightDir, worldNormal);
                        
                        vec3 ambient = vec3(0.1);
                        float diff = max(dot(worldNormal, lightDir), 0.0);
                        vec3 diffuse = diff * vec3(0.8) * uLightIntensity;
                        float spec = pow(max(dot(viewDir, reflectDir), 0.0), 32.0);
                        vec3 specular = spec * vec3(0.3) * uLightIntensity;
                        
                        vec2 grid = floor(vUv);
                        float checker = mod(grid.x + grid.y, 2.0);
                        vec3 baseColor = mix(vec3(0.3), vec3(0.7), checker);
                        
                        gl_FragColor = vec4(baseColor * (ambient + diffuse) + specular, 1.0);
                    }
                `
            }
        };
        
        // 当前程序
        let currentProgram = new Program(gl, {
            vertex: shaders.basic.vertex,
            fragment: shaders.basic.fragment,
            uniforms: {
                uIntensity: { value: 1.0 },
                uLightIntensity: { value: 1.0 },
                uLightPos: { value: [2, 2, 2] }
            }
        });
        
        const mesh = new Mesh(gl, { geometry, program: currentProgram });
        mesh.setParent(scene);

        // ========== 控制面板 ==========
        const controls_ui = {
            technique: document.getElementById('technique'),
            intensity: document.getElementById('intensity'),
            lightIntensity: document.getElementById('lightIntensity'),
            animateLight: document.getElementById('animateLight'),
            intensityValue: document.getElementById('intensityValue'),
            lightValue: document.getElementById('lightValue'),
            techniqueTitle: document.getElementById('techniqueTitle'),
            techniqueDescription: document.getElementById('techniqueDescription')
        };
        
        const techniqueInfo = {
            basic: {
                title: '基础着色',
                description: '标准的Phong光照模型，没有表面细节增强。'
            },
            bump: {
                title: '凹凸贴图 (Bump Mapping)',
                description: '使用程序化高度信息计算法线扰动，创造凹凸感。性能好但效果相对简单。'
            },
            normal: {
                title: '法线贴图 (Normal Mapping)',
                description: '使用程序化法线信息来扰动反射光，创造更精细的凹凸感。必须有光照才能看到效果。'
            }
        };
        
        function updateTechnique() {
            const technique = controls_ui.technique.value;
            const info = techniqueInfo[technique];
            
            controls_ui.techniqueTitle.textContent = info.title;
            controls_ui.techniqueDescription.textContent = info.description;
            
            currentProgram = new Program(gl, {
                vertex: shaders[technique].vertex,
                fragment: shaders[technique].fragment,
                uniforms: {
                    uIntensity: { value: parseFloat(controls_ui.intensity.value) },
                    uLightIntensity: { value: parseFloat(controls_ui.lightIntensity.value) },
                    uLightPos: { value: [2, 2, 2] }
                }
            });
            
            mesh.program = currentProgram;
        }
        
        function updateValues() {
            controls_ui.intensityValue.textContent = controls_ui.intensity.value;
            controls_ui.lightValue.textContent = controls_ui.lightIntensity.value;
            
            if (currentProgram.uniforms.uIntensity) {
                currentProgram.uniforms.uIntensity.value = parseFloat(controls_ui.intensity.value);
            }
            if (currentProgram.uniforms.uLightIntensity) {
                currentProgram.uniforms.uLightIntensity.value = parseFloat(controls_ui.lightIntensity.value);
            }
        }
        
        // 绑定事件
        controls_ui.technique.addEventListener('change', updateTechnique);
        controls_ui.intensity.addEventListener('input', updateValues);
        controls_ui.lightIntensity.addEventListener('input', updateValues);

        // ========== 渲染循环 ==========
        let time = 0;
        
        function resize() {
            renderer.setSize(window.innerWidth, window.innerHeight);
            camera.perspective({ aspect: window.innerWidth / window.innerHeight });
        }
        
        window.addEventListener('resize', resize);
        resize();
        
        requestAnimationFrame(update);
        function update(t) {
            requestAnimationFrame(update);
            time = t * 0.001;
            
            // 动画光源
            if (controls_ui.animateLight.checked) {
                const lightPos = [
                    Math.cos(time) * 3,
                    Math.sin(time * 0.7) * 2 + 1,
                    Math.sin(time) * 3 + 2
                ];
                
                if (currentProgram.uniforms.uLightPos) {
                    currentProgram.uniforms.uLightPos.value = lightPos;
                }
            }
            
            controls.update();
            renderer.render({ scene, camera });
        }
    </script>
</body>
</html>
