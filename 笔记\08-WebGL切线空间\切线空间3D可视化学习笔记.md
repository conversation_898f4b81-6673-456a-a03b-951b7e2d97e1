# 切线空间3D可视化学习笔记

## 📖 概述

本笔记配合 `examples/tangent-space-visualization.html` 3D可视化示例，帮助理解WebGL中的切线空间计算原理。

## 🎯 学习目标

1. **理解切线空间的概念**：TBN矩阵的构成和作用
2. **掌握动态切线计算**：使用偏导数计算切线和副切线
3. **理解UV坐标的作用**：为什么需要UV来确定切线方向
4. **实际应用场景**：法线贴图、凹凸贴图等技术

## 🔍 切线空间 (TBN) 详解

### 什么是切线空间？

切线空间是一个局部坐标系，由三个相互垂直的向量组成：

- **T (Tangent)** 🔴：切线向量，沿着纹理U方向
- **B (Bitangent)** 🟢：副切线向量，沿着纹理V方向  
- **N (Normal)** 🔵：法线向量，垂直于表面

### TBN矩阵的作用

```glsl
mat3 tbn = mat3(tangent, bitangent, normal);
vec3 worldNormal = tbn * tangentSpaceNormal;
```

TBN矩阵将切线空间的向量转换到世界空间，这对法线贴图渲染至关重要。

## 📐 动态切线计算原理

### 核心公式

```glsl
// 计算屏幕空间偏导数
vec3 pos_dx = dFdx(vMPos.xyz);  // 世界位置在X方向的变化率
vec3 pos_dy = dFdy(vMPos.xyz);  // 世界位置在Y方向的变化率
vec2 tex_dx = dFdx(vUv);        // UV坐标在X方向的变化率
vec2 tex_dy = dFdy(vUv);        // UV坐标在Y方向的变化率

// 动态计算切线和副切线
vec3 T = normalize(pos_dx * tex_dy.t - pos_dy * tex_dx.t);
vec3 B = normalize(-pos_dx * tex_dy.s + pos_dy * tex_dx.s);
vec3 N = normalize(cross(T, B));
```

### 数学原理

这个公式来源于线性代数中的逆矩阵计算：

```
[pos_dx]   [tex_dx.s  tex_dx.t] [tangent_u]
[pos_dy] = [tex_dy.s  tex_dy.t] [tangent_v]
```

通过求解这个方程组，我们得到切线向量的计算公式。

## 🎮 3D可视化示例使用指南

### 控制面板功能

1. **显示法线向量 (蓝色)**：显示表面法线方向
2. **显示切线向量 (红色)**：显示纹理U方向的切线
3. **显示副切线向量 (绿色)**：显示纹理V方向的切线
4. **使用法线贴图**：启用动态切线计算效果
5. **向量长度**：调整向量显示的长度
6. **法线强度**：控制法线贴图的强度

### 交互操作

- **鼠标拖拽**：旋转视角观察不同角度
- **滚轮缩放**：放大缩小查看细节
- **勾选选项**：显示/隐藏不同类型的向量

## 🔧 技术实现要点

### 1. 向量可视化系统

```javascript
class VectorVisualizer {
    createVector(position, direction, color, scale) {
        // 创建线段表示向量
        const lineMesh = new Mesh(gl, {
            geometry: this.lineGeometry,
            program: this.vectorProgram,
            mode: gl.LINES,
        });
        
        // 设置位置和方向
        lineMesh.position.copy(position);
        
        // 计算旋转以对齐方向
        const up = new Vec3(0, 1, 0);
        const axis = new Vec3();
        axis.cross(up, direction);
        const angle = Math.acos(up.dot(direction));
        
        if (axis.len() > 0.001) {
            lineMesh.rotation.setAxisAngle(axis.normalize(), angle);
        }
    }
}
```

### 2. 法线贴图纹理生成

```javascript
function createNormalMapTexture() {
    const size = 256;
    const data = new Uint8Array(size * size * 4);
    
    for (let i = 0; i < size; i++) {
        for (let j = 0; j < size; j++) {
            // 创建波浪法线贴图
            const x = (i / size) * Math.PI * 4;
            const y = (j / size) * Math.PI * 4;
            
            const nx = Math.sin(x) * 0.5;
            const ny = Math.sin(y) * 0.5;
            const nz = Math.sqrt(1 - nx * nx - ny * ny);
            
            // 编码到[0,1]范围
            data[index] = (nx * 0.5 + 0.5) * 255;     // R
            data[index + 1] = (ny * 0.5 + 0.5) * 255; // G
            data[index + 2] = (nz * 0.5 + 0.5) * 255; // B
        }
    }
}
```

## 🤔 常见问题解答

### Q1: 为什么需要UV坐标来计算切线？

**A**: UV坐标提供了纹理空间的参考系。没有UV坐标，我们无法确定哪个方向是纹理的"水平"和"垂直"方向，只能计算出任意的表面法线。

### Q2: 什么时候使用动态切线计算？

**A**: 
- ✅ **适用场景**：法线贴图渲染、程序生成几何体、高质量表面细节
- ❌ **不适用**：基础光照、性能敏感应用、没有UV的几何体

### Q3: 动态计算vs预计算切线的优缺点？

**动态计算**：
- ✅ 无需预存储切线属性，节省内存
- ✅ 适用于任意几何体
- ❌ 每个片元都需要计算，性能开销大

**预计算切线**：
- ✅ 性能高，GPU友好
- ✅ 精度稳定
- ❌ 需要额外存储空间
- ❌ 不适用于动态几何体

## 🎯 实际应用场景

### 1. 法线贴图渲染

```glsl
vec3 getNormal() {
    #ifdef NORMAL_MAP
        // 使用动态切线计算
        vec3 pos_dx = dFdx(vMPos.xyz);
        vec3 pos_dy = dFdy(vMPos.xyz);
        vec2 tex_dx = dFdx(vUv);
        vec2 tex_dy = dFdy(vUv);
        
        vec3 t = normalize(pos_dx * tex_dy.t - pos_dy * tex_dx.t);
        vec3 b = normalize(-pos_dx * tex_dy.s + pos_dy * tex_dx.s);
        mat3 tbn = mat3(t, b, normalize(vNormal));
        
        vec3 n = texture2D(tNormal, vUv).rgb * 2.0 - 1.0;
        return normalize(tbn * n);
    #else
        return normalize(vNormal);
    #endif
}
```

### 2. 没有几何体法线时的处理

```glsl
vec3 calculateSurfaceNormal() {
    vec3 pos_dx = dFdx(vWorldPos);
    vec3 pos_dy = dFdy(vWorldPos);
    return normalize(cross(pos_dx, pos_dy));
}
```

## 📚 扩展学习

1. **深入理解偏导数函数**：`dFdx()` 和 `dFdy()` 的GPU实现原理
2. **切线空间的其他应用**：视差贴图、置换贴图
3. **性能优化技巧**：何时使用动态计算vs预计算
4. **高级法线技术**：细节法线、混合法线

## 🔗 相关文件

- `examples/tangent-space-visualization.html` - 3D可视化示例
- `examples/normal-maps.html` - 法线贴图示例
- `examples/load-gltf.html` - GLTF模型加载示例

---

**💡 学习建议**：先运行3D可视化示例，通过交互操作理解概念，再深入研究代码实现。理论结合实践，效果最佳！
